'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { Eye, EyeOff, Lock } from 'lucide-react';
import { validatePasswordComplexity } from '@/lib/password-validation';
import { useToast } from '@/hooks/useToast';

interface ChangePasswordFormProps {
  onSuccess?: () => void;
}

export default function ChangePasswordForm({ onSuccess }: ChangePasswordFormProps) {
  const { data: session } = useSession();
  const { showSuccess, showError } = useToast();
  
  const [formData, setFormData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // Verifica se l'utente è OAuth (non ha password)
  const isOAuthUser = session?.user?.email && !session?.user?.id?.startsWith('user-');

  const handlePasswordChange = (field: 'newPassword', value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Validazione real-time solo per la nuova password
    if (field === 'newPassword' && value.length > 0) {
      const errors = validatePasswordComplexity(value);
      setValidationErrors(errors);
    } else if (field === 'newPassword' && value.length === 0) {
      setValidationErrors([]);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Reset errori precedenti
    setValidationErrors([]);
    
    // Validazione password
    const passwordErrors = validatePasswordComplexity(formData.newPassword);
    if (passwordErrors.length > 0) {
      setValidationErrors(passwordErrors);
      return;
    }
    
    // Verifica che le password coincidano
    if (formData.newPassword !== formData.confirmPassword) {
      setValidationErrors(['La nuova password e la conferma non coincidono']);
      return;
    }
    
    // Verifica che tutti i campi siano compilati
    if (!formData.currentPassword || !formData.newPassword || !formData.confirmPassword) {
      setValidationErrors(['Tutti i campi sono obbligatori']);
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      const response = await fetch('/api/user/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentPassword: formData.currentPassword,
          newPassword: formData.newPassword,
        }),
      });
      
      const data = await response.json();
      
      if (response.ok) {
        showSuccess('Password aggiornata con successo!');
        // Reset del form
        setFormData({
          currentPassword: '',
          newPassword: '',
          confirmPassword: '',
        });
        onSuccess?.();
      } else {
        showError(data.error || 'Errore durante il cambio password');
      }
    } catch (error) {
      console.error('Errore cambio password:', error);
      showError('Errore durante il cambio password');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Se l'utente è OAuth, mostra messaggio informativo
  if (isOAuthUser) {
    return (
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <Lock className="h-5 w-5 text-blue-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              Account collegato tramite Google
            </h3>
            <p className="mt-1 text-sm text-blue-700">
              Il tuo account è collegato tramite Google OAuth. 
              Per modificare la password, utilizza le impostazioni del tuo account Google.
            </p>
            <div className="mt-3">
              <a
                href="https://myaccount.google.com/security"
                target="_blank"
                rel="noopener noreferrer"
                className="text-sm font-medium text-blue-600 hover:text-blue-500"
              >
                Gestisci account Google →
              </a>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-900">Cambia Password</h3>
        <p className="mt-1 text-sm text-gray-600">
          Aggiorna la password del tuo account per mantenere la sicurezza.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Password Attuale */}
        <div>
          <label htmlFor="currentPassword" className="block text-sm font-medium text-gray-700">
            Password Attuale
          </label>
          <div className="mt-1 relative">
            <input
              id="currentPassword"
              name="currentPassword"
              type={showPasswords.current ? 'text' : 'password'}
              required
              value={formData.currentPassword}
              onChange={(e) => setFormData(prev => ({ ...prev, currentPassword: e.target.value }))}
              className="appearance-none relative block w-full px-3 py-2 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
              placeholder="Inserisci la password attuale"
            />
            <button
              type="button"
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
              onClick={() => setShowPasswords(prev => ({ ...prev, current: !prev.current }))}
            >
              {showPasswords.current ? (
                <EyeOff className="h-5 w-5 text-gray-400" />
              ) : (
                <Eye className="h-5 w-5 text-gray-400" />
              )}
            </button>
          </div>
        </div>

        {/* Nuova Password */}
        <div>
          <label htmlFor="newPassword" className="block text-sm font-medium text-gray-700">
            Nuova Password
          </label>
          <div className="mt-1 relative">
            <input
              id="newPassword"
              name="newPassword"
              type={showPasswords.new ? 'text' : 'password'}
              required
              value={formData.newPassword}
              onChange={(e) => handlePasswordChange('newPassword', e.target.value)}
              className="appearance-none relative block w-full px-3 py-2 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
              placeholder="Inserisci la nuova password"
            />
            <button
              type="button"
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
              onClick={() => setShowPasswords(prev => ({ ...prev, new: !prev.new }))}
            >
              {showPasswords.new ? (
                <EyeOff className="h-5 w-5 text-gray-400" />
              ) : (
                <Eye className="h-5 w-5 text-gray-400" />
              )}
            </button>
          </div>
        </div>

        {/* Conferma Nuova Password */}
        <div>
          <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">
            Conferma Nuova Password
          </label>
          <div className="mt-1 relative">
            <input
              id="confirmPassword"
              name="confirmPassword"
              type={showPasswords.confirm ? 'text' : 'password'}
              required
              value={formData.confirmPassword}
              onChange={(e) => setFormData(prev => ({ ...prev, confirmPassword: e.target.value }))}
              className="appearance-none relative block w-full px-3 py-2 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
              placeholder="Conferma la nuova password"
            />
            <button
              type="button"
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
              onClick={() => setShowPasswords(prev => ({ ...prev, confirm: !prev.confirm }))}
            >
              {showPasswords.confirm ? (
                <EyeOff className="h-5 w-5 text-gray-400" />
              ) : (
                <Eye className="h-5 w-5 text-gray-400" />
              )}
            </button>
          </div>
        </div>

        {/* Errori di validazione */}
        {validationErrors.length > 0 && (
          <div className="rounded-md bg-red-50 p-4">
            <div className="text-sm text-red-700">
              <ul className="list-disc list-inside space-y-1">
                {validationErrors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          </div>
        )}

        {/* Requisiti password */}
        <div className="text-sm text-gray-600">
          <p className="font-medium">Requisiti per la nuova password:</p>
          <ul className="mt-1 list-disc list-inside space-y-1">
            <li>Almeno 8 caratteri</li>
            <li>Una lettera maiuscola</li>
            <li>Una lettera minuscola</li>
            <li>Un numero</li>
          </ul>
        </div>

        {/* Pulsante di invio */}
        <div>
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? 'Aggiornamento...' : 'Aggiorna Password'}
          </button>
        </div>
      </form>
    </div>
  );
}
