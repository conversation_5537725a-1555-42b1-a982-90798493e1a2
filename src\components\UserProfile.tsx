'use client';

import { useSession } from 'next-auth/react';
import { User, Mail, Calendar, Shield } from 'lucide-react';
import { UserRole, UserRoleLabels, UserRoleDescriptions } from '@/types/profile';
import UserAvatar from '@/components/UserAvatar';
import ChangePasswordForm from '@/components/ChangePasswordForm';

export default function UserProfile() {
  const { data: session } = useSession();

  if (!session?.user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  const user = session.user;
  const userRole = user.role as UserRole;

  const getRoleBadgeColor = (role: UserRole) => {
    switch (role) {
      case UserRole.Explorer:
        return 'bg-green-100 text-green-800';
      case UserRole.Ranger:
        return 'bg-blue-100 text-blue-800';
      case UserRole.Sentinel:
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Il Mio Profilo</h1>
          <p className="mt-2 text-gray-600">
            Gestisci le informazioni del tuo account e le impostazioni di sicurezza
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Informazioni Profilo */}
          <div className="lg:col-span-1">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="text-center">
                {/* Avatar */}
                <div className="flex justify-center mb-4">
                  <UserAvatar user={user} size="lg" />
                </div>

                {/* Nome e Email */}
                <h2 className="text-xl font-semibold text-gray-900">
                  {user.name || 'Nome non specificato'}
                </h2>
                <p className="text-gray-600 mt-1">{user.email}</p>

                {/* Badge Ruolo */}
                <div className="mt-4">
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getRoleBadgeColor(userRole)}`}>
                    <Shield className="w-4 h-4 mr-1" />
                    {UserRoleLabels[userRole]}
                  </span>
                  <p className="text-sm text-gray-600 mt-2">
                    {UserRoleDescriptions[userRole]}
                  </p>
                </div>
              </div>

              {/* Dettagli Account */}
              <div className="mt-6 border-t border-gray-200 pt-6">
                <dl className="space-y-4">
                  <div className="flex items-center">
                    <dt className="flex items-center text-sm font-medium text-gray-500">
                      <Mail className="w-4 h-4 mr-2" />
                      Email
                    </dt>
                  </div>
                  <dd className="text-sm text-gray-900 ml-6">{user.email}</dd>

                  <div className="flex items-center">
                    <dt className="flex items-center text-sm font-medium text-gray-500">
                      <User className="w-4 h-4 mr-2" />
                      Nome
                    </dt>
                  </div>
                  <dd className="text-sm text-gray-900 ml-6">
                    {user.name || 'Non specificato'}
                  </dd>

                  <div className="flex items-center">
                    <dt className="flex items-center text-sm font-medium text-gray-500">
                      <Calendar className="w-4 h-4 mr-2" />
                      Membro dal
                    </dt>
                  </div>
                  <dd className="text-sm text-gray-900 ml-6">
                    {/* Per ora non abbiamo la data di creazione nella sessione */}
                    Account attivo
                  </dd>
                </dl>
              </div>

              {/* Tipo di Account */}
              <div className="mt-6 border-t border-gray-200 pt-6">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-500">Tipo di Account</span>
                  <span className="text-sm text-gray-900">
                    {user.image ? 'Google OAuth' : 'Email/Password'}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Sezione Sicurezza */}
          <div className="lg:col-span-2">
            <div className="space-y-8">
              {/* Cambio Password */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Sicurezza Account
                </h3>
                <ChangePasswordForm />
              </div>

              {/* Altre impostazioni future */}
              {/* Qui possiamo aggiungere altre sezioni come:
                  - Gestione sessioni attive
                  - Preferenze di notifica
                  - Privacy settings
                  etc.
              */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
